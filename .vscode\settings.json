{
    // Unified Workspace Settings - Quarto & Jupyter Configuration
    // Supports both .qmd and .ipynb workflows with flexible preview options

    // Editor rulers aligned with global settings (80 for code, 120 for text)
    "editor.rulers": [80, 120],

    // Project-specific Python environment (inherits modern Ruff+Black setup from global)
    "python.defaultInterpreterPath": "C:\\AnacondaPath\\envs\\jupyter_env\\python.exe",

    // Python configuration for both notebooks and Quarto
    "[python]": {
        "editor.rulers": [80],
        "editor.wordWrap": "bounded",
        "editor.wordWrapColumn": 80,
        "editor.defaultFormatter": "ms-python.black-formatter"
    },

    // Markdown configuration (for both .qmd text and notebook markdown cells)
    "[markdown]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode",
        "editor.wordWrap": "bounded",
        "editor.wordWrapColumn": 120,
        "editor.rulers": [80, 120],
        // Disable markdown linting to only lint Python code in .qmd files
        "editor.codeActionsOnSave": {},
        "editor.quickSuggestions": {
            "comments": "off",
            "strings": "off",
            "other": "off"
        }
    },

    // Quarto-specific settings for .qmd files
    "[quarto]": {
        "editor.defaultFormatter": "quarto.quarto",
        "editor.wordWrap": "bounded",
        "editor.wordWrapColumn": 120,
        "editor.rulers": [80, 120],
        // Python code in .qmd should use 80 chars, text can use 120
        "python.analysis.typeCheckingMode": "basic"
    },

    // JSON and YAML for configuration files
    "[json]": {
        "editor.tabSize": 2,
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[yaml]": {
        "editor.tabSize": 2,
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },

    // Jupyter-specific settings
    "jupyter.alwaysTrustNotebooks": true,
    "jupyter.askForKernelRestart": false,
    "jupyter.notebookFileRoot": "",
    "jupyter.runStartupCommands": [],

    // Notebook display settings
    "notebook.markup.fontSize": 14,
    "notebook.output.textLineLimit": 30,

    // Quarto rendering settings
    "quarto.render.renderOnSave": false,

    // Quarto executable path configuration
    "quarto.path": "C:\\QUARTO\\quarto_1_7_32\\bin\\quarto.cmd",

    // Preview server configuration
    "quarto.preview.port": 8080,

    // Preview settings - Internal VS Code preview configuration
    "quarto.render.previewType": "internal",
    "quarto.render.previewSideBySide": true,
    "quarto.render.previewLocation": "beside",
    "jupyter.interactiveWindow.textEditor.executeSelection": true,
    "jupyter.exportWithOutputs": true,
    "notebook.lineNumbers": "on",

    // Ensure external browser is disabled
    // "quarto.render.previewType": "external",

    // Optional: Custom preview behavior
    // "quarto.mathjax.theme": "default",
    // "quarto.assist.activate": true,

    // File associations for both workflows
    "files.associations": {
        "*.qmd": "quarto",
        "*.ipynb": "jupyter-notebook",
        "*.csv": "csv (semicolon)"
    },

    // Project-specific spell checker words (combined list)
    "cSpell.words": [
        "projectword",
        "numpy",
        "pandas",
        "matplotlib",
        "quarto",
        "ipynb",
        "qmd"
    ],

    // Search exclusions (comprehensive for both workflows)
    "search.exclude": {
        "**/.qmd_cache": true,
        "**/~$*.txt": true,
        "**/_freeze": true,
        "**/.ipynb_checkpoints": true,
        "**/data/*.csv": true,
        "**/output": true,
        "**/__pycache__": true,
        "**/node_modules": true,
        "**/.venv": true,
        "**/build": true,
        "**/dist": true
    },

    // Terminal settings (preserving from general settings)
    "terminal.integrated.defaultProfile.windows": "Command Prompt"
}
