---
title: "Logo Implementation Guide"
subtitle: "Quarto Best Practices for AVJ Naturfond"
date: now
date-fomrat: iso
---

# Logo Usage in Quarto Documents

This document demonstrates the best practices for including the AVJ Naturfond logo in Quarto documents that render to both HTML and Jupyter notebooks.

## Method 1: HTML Image Tag (Recommended)

The most reliable cross-format approach is using a standard HTML img tag with the `.avj-logo` class:

```html
<div class="avj-logo">
  <img src="AVJ_NATURFOND_RGB.png" alt="Aage V. Jensen Naturfond Logo" />
</div>
```

<div class="avj-logo">
  <img src="AVJ_NATURFOND_RGB.png" alt="Aage V. Jensen Naturfond Logo" />
</div>

## Method 2: Markdown Image (Alternative)

For simple cases, you can also use Markdown syntax with custom classes:

```markdown
![Aage V. Jensen Naturfond Logo](AVJ_NATURFOND_RGB.png){.logo}
```

![Aage V. Jensen Naturfond Logo](AVJ_NATURFOND_RGB.png){.logo}

## Method 3: Background Image Approach

For layouts where you need text to wrap around the logo area:

```html
<div class="avj-logo-bg">
  <h1>Your Content Here</h1>
  <p>This content will have space reserved for the background logo.</p>
</div>
```

<div class="avj-logo-bg">
  <h1>Example Heading</h1>
  <p>This content demonstrates how text flows with the background logo approach.</p>
</div>

## YAML Header Method (Document-wide)

For consistent logo placement across the entire document, add this to your YAML header:

```yaml
format:
  html:
    theme:
      - default
      - styles.scss
    include-in-header: |
      <div class="avj-logo">
        <img src="AVJ_NATURFOND_RGB.png" alt="Aage V. Jensen Naturfond Logo" />
      </div>
```

## Key Improvements

1. **Cross-format compatibility**: Works in HTML, PDF, and Jupyter notebooks
2. **Accessibility**: Proper alt text for screen readers
3. **Responsive design**: Scales appropriately on mobile devices
4. **Print-friendly**: Optimized for print output
5. **Path flexibility**: Uses relative paths that work in different contexts
6. **Semantic markup**: Uses proper HTML structure instead of CSS content injection

## Usage Recommendations

- Use **Method 1** (HTML img tag) for most cases
- Use **Method 2** (Markdown) for simple, inline logos
- Use **Method 3** (background) when you need text to flow around the logo area
- Use **YAML header method** for document-wide logo placement

The refactored CSS now provides robust, cross-platform logo rendering that follows Quarto best practices.
