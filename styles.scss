/*-- scss:defaults --*/
/* Aage V<PERSON> Jensen Naturfond custom theme variables */

$avj-primary: #046A38 !default;
$avj-light-bg: #f9f9f9 !default;
$avj-border-color: #ccc !default;

/* Override Bootstrap variables for consistent theming */
$primary: $avj-primary !default;
$font-family-sans-serif: "Calibri", system-ui, -apple-system, sans-serif !default;
$line-height-base: 1.6 !default;
$body-color: #333 !default;
$max-width: 1200px !default;
$link-color: $avj-primary !default;

/* Heading variables */
$headings-color: $avj-primary !default;
$headings-margin-bottom: 0.5rem !default;
$h1-font-size: 2.5rem !default;
$h2-font-size: 2rem !default;

/* Callout and blockquote styling */
$blockquote-border-color: $avj-primary !default;
$blockquote-bg: $avj-light-bg !default;

/*-- scss:rules --*/
/* <PERSON><PERSON> V. Jensen Naturfond custom styles */

/* Global layout and typography */
body {
  font-family: "Calibri", system-ui, -apple-system, sans-serif;
  line-height: 1.6;
  color: #333;
  max-width: 1200px;
  margin: 0 auto;
}

/* Heading styles with brand consistency */
h1, h2, h3, h4, h5, h6 {
  color: #046A38;
  margin-top: 1.5em;
}

h1 {
  border-bottom: 2px solid #046A38;
  padding-bottom: 0.2em;
  clear: both;
}

h2 {
  border-bottom: 1px solid #ccc;
  padding-bottom: 0.1em;
}

/* Enhanced blockquote styling */
blockquote {
  background-color: #f9f9f9;
  border-left: 4px solid #046A38;
  margin: 1em 0;
  padding: 0.5em 1rem;
  font-style: italic;
  border-radius: 0 0.25rem 0.25rem 0;
}

/* Logo styling with automatic path inclusion */
.logo {
  float: right;
  margin: 10px;
  max-width: 250px;
  height: auto;
}

.logo-container {
  text-align: right;
  margin-top: 10px;
  margin-bottom: 30px;
  overflow: hidden;
}

/* Logo styling - Quarto best practice approach */
.avj-logo {
  position: relative;
  min-height: 80px; /* Ensure space for logo */
}

.avj-logo img {
  float: right;
  margin: 10px;
  max-width: 250px;
  height: auto;
  clear: both;
}

/* Alternative background approach for when img tag isn't suitable */
.avj-logo-bg {
  background-image: url("AVJ_NATURFOND_RGB.png");
  background-repeat: no-repeat;
  background-position: top right;
  background-size: auto 250px;
  min-height: 100px;
  padding-right: 270px; /* Space for logo */
}

/* Ensure proper spacing between logo and the first heading */
h1:first-of-type {
  margin-top: 10px;
  clear: both;
}

/* Add clear for h2 headings that follow the logo */
h2:first-of-type {
  clear: both;
  margin-top: 10px;
}

/* Clear any heading that follows the logo */
.avj-logo + h1,
.avj-logo + h2,
.avj-logo + h3 {
  clear: both;
  margin-top: 10px;
}

/* Enhanced callout styling following Quarto conventions */
.callout-note {
  background-color: #f8f9fa;
  border-left: 5px solid #0c5460;
  padding: 1em;
  margin: 1em 0;
  border-radius: 0 0.25rem 0.25rem 0;
}

.callout-important {
  background-color: #f8f9fa;
  border-left: 5px solid #721c24;
  padding: 1em;
  margin: 1em 0;
  border-radius: 0 0.25rem 0.25rem 0;
}

/* Conclusion styling with AVJ branding */
.conclusion {
  background-color: #f8f9fa;
  border: 1px solid #046A38;
  border-radius: 5px;
  padding: 1.5em;
  margin-top: 2em;
  font-style: italic;
  position: relative;
}

.conclusion::before {
  content: "Konklusion";
  position: absolute;
  top: -12px;
  left: 15px;
  background-color: #046A38;
  color: white;
  padding: 2px 10px;
  border-radius: 3px;
  font-size: 0.85em;
  font-weight: bold;
  font-style: normal;
}

/* Enhanced table styling */
.table-responsive {
  overflow-x: auto;
  margin: 1em 0;
  border-radius: 0.25rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

table {
  border-collapse: collapse;
  width: 100%;
  margin: 1em 0;
  background-color: white;
}

table, th, td {
  border: 1px solid #ddd;
}

th {
  background-color: #f2f2f2;
  text-align: left;
  padding: 12px 8px;
  font-weight: 600;
  color: #046A38;
  border-bottom: 2px solid #046A38;
}

td {
  padding: 10px 8px;
  vertical-align: top;
}

tr:nth-child(even) {
  background-color: #f9f9f9;
}

tr:hover {
  background-color: #f5f5f5;
}

/* Responsive design improvements */
@media (max-width: 768px) {
  .logo, .avj-logo img {
    max-width: 150px;
    float: none;
    display: block;
    margin: 10px auto;
  }

  .avj-logo-bg {
    background-size: auto 150px;
    background-position: center top;
    padding-right: 0;
    text-align: center;
    min-height: 80px;
  }

  .logo-container {
    text-align: center;
  }

  body {
    padding: 0 1rem;
  }

  table {
    font-size: 0.9em;
  }

  th, td {
    padding: 8px 4px;
  }
}

/* Print styles */
@media print {
  .logo, .avj-logo img {
    max-width: 200px;
  }

  .avj-logo-bg {
    background-size: auto 200px;
  }

  body {
    font-size: 12pt;
    line-height: 1.4;
  }

  h1, h2, h3 {
    page-break-after: avoid;
  }

  .callout-note, .callout-important, .conclusion {
    page-break-inside: avoid;
  }
}

/* Additional utility classes for Quarto compatibility */
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-left { text-align: left; }

.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 1rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }

/* Enhanced focus and accessibility */
a:focus, button:focus, input:focus, textarea:focus, select:focus {
  outline: 2px solid #046A38;
  outline-offset: 2px;
}

/* Dark mode support (if using Quarto's dark mode) */
@media (prefers-color-scheme: dark) {
  .callout-note, .callout-important, .conclusion {
    background-color: rgba(248, 249, 250, 0.1);
  }

  .conclusion::before {
    background-color: #059862;
  }
}
