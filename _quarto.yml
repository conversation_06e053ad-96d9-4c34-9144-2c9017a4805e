lang: da
language:
    crossref-fig-title: "Figur"
    crossref-tbl-title: "Tabel"
    crossref-eq-prefix: "Ligning"
    crossref-sec-prefix: "Afsnit"

# Project profiles for different use cases
project:
    type: default

format:
    html:
        theme:
            - default
            - styles.scss
        code-fold: true
        code-tools: true
        code-line-numbers: true
        code-overflow: wrap
        toc: true
        toc-title: "Indholdsfortegnelse"
        toc-depth: 2
        toc-location: left
        number-sections: true
        highlight-style: github
        embed-resources: false # Development mode: faster rendering
        self-contained: false # Development mode: lighter processing
        anchor-sections: true
        smooth-scroll: true
        fig-cap-location: bottom
        tbl-cap-location: top

    # Distribution format for sharing
    html-dist:
        theme:
            - default
            - styles.scss
        code-fold: true
        code-tools: true
        code-line-numbers: true
        code-overflow: wrap
        toc: true
        toc-title: "Indholdsfortegnelse"
        toc-depth: 2
        toc-location: left
        number-sections: true
        highlight-style: github
        embed-resources: true # Distribution mode: everything embedded
        self-contained: true # Distribution mode: single file
        anchor-sections: true
        smooth-scroll: true
        fig-cap-location: bottom
        tbl-cap-location: top
    pdf:
        toc: true
        toc-title: "Indholdsfortegnelse"
        number-sections: true
        colorlinks: true
        keep-tex: false
        documentclass: article
        papersize: a4
        geometry:
            - margin=2.5cm
        lang: da
        babel-lang: danish

execute:
    echo: true
    warning: false
    message: false
    cache: true

jupyter: python3

editor:
    markdown:
        wrap: 120
        canonical: true

# Corporate firewall optimized preview settings
preview:
    port: 8080
    browser: false
    navigate: false
    watch-inputs: true
    timeout: 30
