#!/bin/bash
# Quick command aliases for different rendering modes

# Development mode (fast, with dependencies)
alias qdev="quarto render"

# Distribution mode (slow, self-contained)
alias qdist="quarto render --to html-dist"

# Package with dependencies for sharing
qpackage() {
    if [ -z "$1" ]; then
        echo "Usage: qpackage document-name"
        return 1
    fi

    local doc="$1"
    echo "Creating distribution package for $doc..."

    # Render with dependencies
    quarto render "$doc.qmd"

    # Create ZIP with HTML and dependencies
    if [ -f "$doc.html" ]; then
        zip -r "${doc}-distribution.zip" "$doc.html" "${doc}_files" 2>/dev/null
        echo "Package created: ${doc}-distribution.zip"
    else
        echo "Error: $doc.html not found"
    fi
}

echo "Available commands:"
echo "  qdev filename.qmd     - Fast development render"
echo "  qdist filename.qmd    - Self-contained distribution render"
echo "  qpackage filename     - Package with dependencies for sharing"
