@echo off
REM Script to package HTML document with dependencies for sharing
REM Usage: package.bat document-name

if "%1"=="" (
    echo Usage: package.bat document-name
    echo Example: package.bat plejeplan_hornbaek
    exit /b 1
)

set DOCNAME=%1

if not exist "%DOCNAME%.html" (
    echo Error: %DOCNAME%.html not found
    exit /b 1
)

echo Packaging %DOCNAME% for distribution...

REM Create distribution directory
if exist "dist" rmdir /S /Q "dist"
mkdir "dist"

REM Copy HTML file and dependencies
copy "%DOCNAME%.html" "dist\"
if exist "%DOCNAME%_files" xcopy "%DOCNAME%_files" "dist\%DOCNAME%_files" /E /I /H

REM Create ZIP file
powershell -command "Compress-Archive -Path 'dist\*' -DestinationPath '%DOCNAME%-distribution.zip' -Force"

echo Package created: %DOCNAME%-distribution.zip
echo Ready to share!

REM Clean up
rmdir /S /Q "dist"
