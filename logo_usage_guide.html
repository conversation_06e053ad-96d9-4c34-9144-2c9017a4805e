<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="da" xml:lang="da"><head>

<meta charset="utf-8">
<meta name="generator" content="quarto-1.7.32">

<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes">


<title>Logo Implementation Guide</title>
<style>
code{white-space: pre-wrap;}
span.smallcaps{font-variant: small-caps;}
div.columns{display: flex; gap: min(4vw, 1.5em);}
div.column{flex: auto; overflow-x: auto;}
div.hanging-indent{margin-left: 1.5em; text-indent: -1.5em;}
ul.task-list{list-style: none;}
ul.task-list li input[type="checkbox"] {
  width: 0.8em;
  margin: 0 0.8em 0.2em -1em; /* quarto-specific, see https://github.com/quarto-dev/quarto-cli/issues/4556 */ 
  vertical-align: middle;
}
/* CSS for syntax highlighting */
html { -webkit-text-size-adjust: 100%; }
pre > code.sourceCode { white-space: pre; position: relative; }
pre > code.sourceCode > span { display: inline-block; line-height: 1.25; }
pre > code.sourceCode > span:empty { height: 1.2em; }
.sourceCode { overflow: visible; }
code.sourceCode > span { color: inherit; text-decoration: inherit; }
div.sourceCode { margin: 1em 0; }
pre.sourceCode { margin: 0; }
@media screen {
div.sourceCode { overflow: auto; }
}
@media print {
pre > code.sourceCode { white-space: pre-wrap; }
pre > code.sourceCode > span { text-indent: -5em; padding-left: 5em; }
}
pre.numberSource code
  { counter-reset: source-line 0; }
pre.numberSource code > span
  { position: relative; left: -4em; counter-increment: source-line; }
pre.numberSource code > span > a:first-child::before
  { content: counter(source-line);
    position: relative; left: -1em; text-align: right; vertical-align: baseline;
    border: none; display: inline-block;
    -webkit-touch-callout: none; -webkit-user-select: none;
    -khtml-user-select: none; -moz-user-select: none;
    -ms-user-select: none; user-select: none;
    padding: 0 4px; width: 4em;
  }
pre.numberSource { margin-left: 3em;  padding-left: 4px; }
div.sourceCode
  {   }
@media screen {
pre > code.sourceCode > span > a:first-child::before { text-decoration: underline; }
}
</style>


<script src="logo_usage_guide_files/libs/clipboard/clipboard.min.js"></script>
<script src="logo_usage_guide_files/libs/quarto-html/quarto.js" type="module"></script>
<script src="logo_usage_guide_files/libs/quarto-html/tabsets/tabsets.js" type="module"></script>
<script src="logo_usage_guide_files/libs/quarto-html/popper.min.js"></script>
<script src="logo_usage_guide_files/libs/quarto-html/tippy.umd.min.js"></script>
<script src="logo_usage_guide_files/libs/quarto-html/anchor.min.js"></script>
<link href="logo_usage_guide_files/libs/quarto-html/tippy.css" rel="stylesheet">
<link href="logo_usage_guide_files/libs/quarto-html/quarto-syntax-highlighting-234273d1456647dabc34a594ac50e507.css" rel="stylesheet" id="quarto-text-highlighting-styles">
<script src="logo_usage_guide_files/libs/bootstrap/bootstrap.min.js"></script>
<link href="logo_usage_guide_files/libs/bootstrap/bootstrap-icons.css" rel="stylesheet">
<link href="logo_usage_guide_files/libs/bootstrap/bootstrap-d526fbf0023e25bd132b3e240051b431.min.css" rel="stylesheet" append-hash="true" id="quarto-bootstrap" data-mode="light">
<style>html{ scroll-behavior: smooth; }</style>


</head>

<body class="quarto-light">

<div id="quarto-content" class="page-columns page-rows-contents page-layout-article toc-left">
<div id="quarto-sidebar-toc-left" class="sidebar toc-left">
  <nav id="TOC" role="doc-toc" class="toc-active">
    <h2 id="toc-title">Indholdsfortegnelse</h2>
   
  <ul class="collapse">
  <li><a href="#logo-usage-in-quarto-documents" id="toc-logo-usage-in-quarto-documents" class="nav-link active" data-scroll-target="#logo-usage-in-quarto-documents"><span class="header-section-number">1</span> Logo Usage in Quarto Documents</a>
  <ul class="collapse">
  <li><a href="#method-1-html-image-tag-recommended" id="toc-method-1-html-image-tag-recommended" class="nav-link" data-scroll-target="#method-1-html-image-tag-recommended"><span class="header-section-number">1.1</span> Method 1: HTML Image Tag (Recommended)</a></li>
  <li><a href="#method-2-markdown-image-alternative" id="toc-method-2-markdown-image-alternative" class="nav-link" data-scroll-target="#method-2-markdown-image-alternative"><span class="header-section-number">1.2</span> Method 2: Markdown Image (Alternative)</a></li>
  <li><a href="#method-3-background-image-approach" id="toc-method-3-background-image-approach" class="nav-link" data-scroll-target="#method-3-background-image-approach"><span class="header-section-number">1.3</span> Method 3: Background Image Approach</a></li>
  <li><a href="#yaml-header-method-document-wide" id="toc-yaml-header-method-document-wide" class="nav-link" data-scroll-target="#yaml-header-method-document-wide"><span class="header-section-number">1.4</span> YAML Header Method (Document-wide)</a></li>
  <li><a href="#key-improvements" id="toc-key-improvements" class="nav-link" data-scroll-target="#key-improvements"><span class="header-section-number">1.5</span> Key Improvements</a></li>
  <li><a href="#usage-recommendations" id="toc-usage-recommendations" class="nav-link" data-scroll-target="#usage-recommendations"><span class="header-section-number">1.6</span> Usage Recommendations</a></li>
  </ul></li>
  </ul>
<div class="quarto-alternate-formats"><h2>Andre formater</h2><ul><li><a href="logo_usage_guide.pdf"><i class="bi bi-file-pdf"></i>PDF</a></li></ul></div></nav>
</div>
<div id="quarto-margin-sidebar" class="sidebar margin-sidebar zindex-bottom">
</div>
<main class="content" id="quarto-document-content">

<header id="title-block-header" class="quarto-title-block default">
<div class="quarto-title">
<div class="quarto-title-block"><div><h1 class="title">Logo Implementation Guide</h1><button type="button" class="btn code-tools-button" id="quarto-code-tools-source"><i class="bi"></i> Kode</button></div></div>
<p class="subtitle lead">Quarto Best Practices for AVJ Naturfond</p>
</div>



<div class="quarto-title-meta">

    
    <div>
    <div class="quarto-title-meta-heading">Udgivet</div>
    <div class="quarto-title-meta-contents">
      <p class="date">13. juli 2025</p>
    </div>
  </div>
  
    
  </div>
  


</header>


<section id="logo-usage-in-quarto-documents" class="level1" data-number="1">
<h1 data-number="1"><span class="header-section-number">1</span> Logo Usage in Quarto Documents</h1>
<p>This document demonstrates the best practices for including the AVJ Naturfond logo in Quarto documents that render to both HTML and Jupyter notebooks.</p>
<section id="method-1-html-image-tag-recommended" class="level2" data-number="1.1">
<h2 data-number="1.1" class="anchored" data-anchor-id="method-1-html-image-tag-recommended"><span class="header-section-number">1.1</span> Method 1: HTML Image Tag (Recommended)</h2>
<p>The most reliable cross-format approach is using a standard HTML img tag with the <code>.avj-logo</code> class:</p>
<div class="sourceCode" id="cb1"><pre class="sourceCode numberSource html number-lines code-with-copy"><code class="sourceCode html"><span id="cb1-1"><a href="#cb1-1"></a><span class="dt">&lt;</span><span class="kw">div</span><span class="ot"> class</span><span class="op">=</span><span class="st">"avj-logo"</span><span class="dt">&gt;</span></span>
<span id="cb1-2"><a href="#cb1-2"></a>  <span class="dt">&lt;</span><span class="kw">img</span><span class="ot"> src</span><span class="op">=</span><span class="st">"AVJ_NATURFOND_RGB.png"</span><span class="ot"> alt</span><span class="op">=</span><span class="st">"Aage V. Jensen Naturfond Logo"</span><span class="ot"> </span><span class="dt">/&gt;</span></span>
<span id="cb1-3"><a href="#cb1-3"></a><span class="dt">&lt;/</span><span class="kw">div</span><span class="dt">&gt;</span></span></code><button title="Kopier til udklipsholder" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="avj-logo">
<p><img src="AVJ_NATURFOND_RGB.png" alt="Aage V. Jensen Naturfond Logo"></p>
</div>
</section>
<section id="method-2-markdown-image-alternative" class="level2" data-number="1.2">
<h2 data-number="1.2" class="anchored" data-anchor-id="method-2-markdown-image-alternative"><span class="header-section-number">1.2</span> Method 2: Markdown Image (Alternative)</h2>
<p>For simple cases, you can also use Markdown syntax with custom classes:</p>
<div class="sourceCode" id="cb2"><pre class="sourceCode numberSource markdown number-lines code-with-copy"><code class="sourceCode markdown"><span id="cb2-1"><a href="#cb2-1"></a><span class="al">![Aage V. Jensen Naturfond Logo](AVJ_NATURFOND_RGB.png)</span>{.logo}</span></code><button title="Kopier til udklipsholder" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="quarto-figure quarto-figure-center">
<figure class="figure">
<p><img src="AVJ_NATURFOND_RGB.png" class="logo img-fluid figure-img"></p>
<figcaption>Aage V. Jensen Naturfond Logo</figcaption>
</figure>
</div>
</section>
<section id="method-3-background-image-approach" class="level2" data-number="1.3">
<h2 data-number="1.3" class="anchored" data-anchor-id="method-3-background-image-approach"><span class="header-section-number">1.3</span> Method 3: Background Image Approach</h2>
<p>For layouts where you need text to wrap around the logo area:</p>
<div class="sourceCode" id="cb3"><pre class="sourceCode numberSource html number-lines code-with-copy"><code class="sourceCode html"><span id="cb3-1"><a href="#cb3-1"></a><span class="dt">&lt;</span><span class="kw">div</span><span class="ot"> class</span><span class="op">=</span><span class="st">"avj-logo-bg"</span><span class="dt">&gt;</span></span>
<span id="cb3-2"><a href="#cb3-2"></a>  <span class="dt">&lt;</span><span class="kw">h1</span><span class="dt">&gt;</span>Your Content Here<span class="dt">&lt;/</span><span class="kw">h1</span><span class="dt">&gt;</span></span>
<span id="cb3-3"><a href="#cb3-3"></a>  <span class="dt">&lt;</span><span class="kw">p</span><span class="dt">&gt;</span>This content will have space reserved for the background logo.<span class="dt">&lt;/</span><span class="kw">p</span><span class="dt">&gt;</span></span>
<span id="cb3-4"><a href="#cb3-4"></a><span class="dt">&lt;/</span><span class="kw">div</span><span class="dt">&gt;</span></span></code><button title="Kopier til udklipsholder" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="avj-logo-bg">
<h1>
Example Heading
</h1>
<p>
This content demonstrates how text flows with the background logo approach.
</p>
</div>
</section>
<section id="yaml-header-method-document-wide" class="level2" data-number="1.4">
<h2 data-number="1.4" class="anchored" data-anchor-id="yaml-header-method-document-wide"><span class="header-section-number">1.4</span> YAML Header Method (Document-wide)</h2>
<p>For consistent logo placement across the entire document, add this to your YAML header:</p>
<div class="sourceCode" id="cb4"><pre class="sourceCode numberSource yaml number-lines code-with-copy"><code class="sourceCode yaml"><span id="cb4-1"><a href="#cb4-1"></a><span class="fu">format</span><span class="kw">:</span></span>
<span id="cb4-2"><a href="#cb4-2"></a><span class="at">  </span><span class="fu">html</span><span class="kw">:</span></span>
<span id="cb4-3"><a href="#cb4-3"></a><span class="at">    </span><span class="fu">theme</span><span class="kw">:</span></span>
<span id="cb4-4"><a href="#cb4-4"></a><span class="at">      </span><span class="kw">-</span><span class="at"> default</span></span>
<span id="cb4-5"><a href="#cb4-5"></a><span class="at">      </span><span class="kw">-</span><span class="at"> styles.scss</span></span>
<span id="cb4-6"><a href="#cb4-6"></a><span class="fu">    include-in-header</span><span class="kw">: </span><span class="ch">|</span></span>
<span id="cb4-7"><a href="#cb4-7"></a>      &lt;div class="avj-logo"&gt;</span>
<span id="cb4-8"><a href="#cb4-8"></a>        &lt;img src="AVJ_NATURFOND_RGB.png" alt="Aage V. Jensen Naturfond Logo" /&gt;</span>
<span id="cb4-9"><a href="#cb4-9"></a>      &lt;/div&gt;</span></code><button title="Kopier til udklipsholder" class="code-copy-button"><i class="bi"></i></button></pre></div>
</section>
<section id="key-improvements" class="level2" data-number="1.5">
<h2 data-number="1.5" class="anchored" data-anchor-id="key-improvements"><span class="header-section-number">1.5</span> Key Improvements</h2>
<ol type="1">
<li><strong>Cross-format compatibility</strong>: Works in HTML, PDF, and Jupyter notebooks</li>
<li><strong>Accessibility</strong>: Proper alt text for screen readers</li>
<li><strong>Responsive design</strong>: Scales appropriately on mobile devices</li>
<li><strong>Print-friendly</strong>: Optimized for print output</li>
<li><strong>Path flexibility</strong>: Uses relative paths that work in different contexts</li>
<li><strong>Semantic markup</strong>: Uses proper HTML structure instead of CSS content injection</li>
</ol>
</section>
<section id="usage-recommendations" class="level2" data-number="1.6">
<h2 data-number="1.6" class="anchored" data-anchor-id="usage-recommendations"><span class="header-section-number">1.6</span> Usage Recommendations</h2>
<ul>
<li>Use <strong>Method 1</strong> (HTML img tag) for most cases</li>
<li>Use <strong>Method 2</strong> (Markdown) for simple, inline logos</li>
<li>Use <strong>Method 3</strong> (background) when you need text to flow around the logo area</li>
<li>Use <strong>YAML header method</strong> for document-wide logo placement</li>
</ul>
<p>The refactored CSS now provides robust, cross-platform logo rendering that follows Quarto best practices.</p>
<!-- -->

</section>
</section>

</main>
<!-- /main column -->
<script id="quarto-html-after-body" type="application/javascript">
  window.document.addEventListener("DOMContentLoaded", function (event) {
    const icon = "";
    const anchorJS = new window.AnchorJS();
    anchorJS.options = {
      placement: 'right',
      icon: icon
    };
    anchorJS.add('.anchored');
    const isCodeAnnotation = (el) => {
      for (const clz of el.classList) {
        if (clz.startsWith('code-annotation-')) {                     
          return true;
        }
      }
      return false;
    }
    const onCopySuccess = function(e) {
      // button target
      const button = e.trigger;
      // don't keep focus
      button.blur();
      // flash "checked"
      button.classList.add('code-copy-button-checked');
      var currentTitle = button.getAttribute("title");
      button.setAttribute("title", "Kopieret!");
      let tooltip;
      if (window.bootstrap) {
        button.setAttribute("data-bs-toggle", "tooltip");
        button.setAttribute("data-bs-placement", "left");
        button.setAttribute("data-bs-title", "Kopieret!");
        tooltip = new bootstrap.Tooltip(button, 
          { trigger: "manual", 
            customClass: "code-copy-button-tooltip",
            offset: [0, -8]});
        tooltip.show();    
      }
      setTimeout(function() {
        if (tooltip) {
          tooltip.hide();
          button.removeAttribute("data-bs-title");
          button.removeAttribute("data-bs-toggle");
          button.removeAttribute("data-bs-placement");
        }
        button.setAttribute("title", currentTitle);
        button.classList.remove('code-copy-button-checked');
      }, 1000);
      // clear code selection
      e.clearSelection();
    }
    const getTextToCopy = function(trigger) {
        const codeEl = trigger.previousElementSibling.cloneNode(true);
        for (const childEl of codeEl.children) {
          if (isCodeAnnotation(childEl)) {
            childEl.remove();
          }
        }
        return codeEl.innerText;
    }
    const clipboard = new window.ClipboardJS('.code-copy-button:not([data-in-quarto-modal])', {
      text: getTextToCopy
    });
    clipboard.on('success', onCopySuccess);
    if (window.document.getElementById('quarto-embedded-source-code-modal')) {
      const clipboardModal = new window.ClipboardJS('.code-copy-button[data-in-quarto-modal]', {
        text: getTextToCopy,
        container: window.document.getElementById('quarto-embedded-source-code-modal')
      });
      clipboardModal.on('success', onCopySuccess);
    }
    const viewSource = window.document.getElementById('quarto-view-source') ||
                       window.document.getElementById('quarto-code-tools-source');
    if (viewSource) {
      const sourceUrl = viewSource.getAttribute("data-quarto-source-url");
      viewSource.addEventListener("click", function(e) {
        if (sourceUrl) {
          // rstudio viewer pane
          if (/\bcapabilities=\b/.test(window.location)) {
            window.open(sourceUrl);
          } else {
            window.location.href = sourceUrl;
          }
        } else {
          const modal = new bootstrap.Modal(document.getElementById('quarto-embedded-source-code-modal'));
          modal.show();
        }
        return false;
      });
    }
    function toggleCodeHandler(show) {
      return function(e) {
        const detailsSrc = window.document.querySelectorAll(".cell > details > .sourceCode");
        for (let i=0; i<detailsSrc.length; i++) {
          const details = detailsSrc[i].parentElement;
          if (show) {
            details.open = true;
          } else {
            details.removeAttribute("open");
          }
        }
        const cellCodeDivs = window.document.querySelectorAll(".cell > .sourceCode");
        const fromCls = show ? "hidden" : "unhidden";
        const toCls = show ? "unhidden" : "hidden";
        for (let i=0; i<cellCodeDivs.length; i++) {
          const codeDiv = cellCodeDivs[i];
          if (codeDiv.classList.contains(fromCls)) {
            codeDiv.classList.remove(fromCls);
            codeDiv.classList.add(toCls);
          } 
        }
        return false;
      }
    }
    const hideAllCode = window.document.getElementById("quarto-hide-all-code");
    if (hideAllCode) {
      hideAllCode.addEventListener("click", toggleCodeHandler(false));
    }
    const showAllCode = window.document.getElementById("quarto-show-all-code");
    if (showAllCode) {
      showAllCode.addEventListener("click", toggleCodeHandler(true));
    }
      var localhostRegex = new RegExp(/^(?:http|https):\/\/localhost\:?[0-9]*\//);
      var mailtoRegex = new RegExp(/^mailto:/);
        var filterRegex = new RegExp('/' + window.location.host + '/');
      var isInternal = (href) => {
          return filterRegex.test(href) || localhostRegex.test(href) || mailtoRegex.test(href);
      }
      // Inspect non-navigation links and adorn them if external
     var links = window.document.querySelectorAll('a[href]:not(.nav-link):not(.navbar-brand):not(.toc-action):not(.sidebar-link):not(.sidebar-item-toggle):not(.pagination-link):not(.no-external):not([aria-hidden]):not(.dropdown-item):not(.quarto-navigation-tool):not(.about-link)');
      for (var i=0; i<links.length; i++) {
        const link = links[i];
        if (!isInternal(link.href)) {
          // undo the damage that might have been done by quarto-nav.js in the case of
          // links that we want to consider external
          if (link.dataset.originalHref !== undefined) {
            link.href = link.dataset.originalHref;
          }
        }
      }
    function tippyHover(el, contentFn, onTriggerFn, onUntriggerFn) {
      const config = {
        allowHTML: true,
        maxWidth: 500,
        delay: 100,
        arrow: false,
        appendTo: function(el) {
            return el.parentElement;
        },
        interactive: true,
        interactiveBorder: 10,
        theme: 'quarto',
        placement: 'bottom-start',
      };
      if (contentFn) {
        config.content = contentFn;
      }
      if (onTriggerFn) {
        config.onTrigger = onTriggerFn;
      }
      if (onUntriggerFn) {
        config.onUntrigger = onUntriggerFn;
      }
      window.tippy(el, config); 
    }
    const noterefs = window.document.querySelectorAll('a[role="doc-noteref"]');
    for (var i=0; i<noterefs.length; i++) {
      const ref = noterefs[i];
      tippyHover(ref, function() {
        // use id or data attribute instead here
        let href = ref.getAttribute('data-footnote-href') || ref.getAttribute('href');
        try { href = new URL(href).hash; } catch {}
        const id = href.replace(/^#\/?/, "");
        const note = window.document.getElementById(id);
        if (note) {
          return note.innerHTML;
        } else {
          return "";
        }
      });
    }
    const xrefs = window.document.querySelectorAll('a.quarto-xref');
    const processXRef = (id, note) => {
      // Strip column container classes
      const stripColumnClz = (el) => {
        el.classList.remove("page-full", "page-columns");
        if (el.children) {
          for (const child of el.children) {
            stripColumnClz(child);
          }
        }
      }
      stripColumnClz(note)
      if (id === null || id.startsWith('sec-')) {
        // Special case sections, only their first couple elements
        const container = document.createElement("div");
        if (note.children && note.children.length > 2) {
          container.appendChild(note.children[0].cloneNode(true));
          for (let i = 1; i < note.children.length; i++) {
            const child = note.children[i];
            if (child.tagName === "P" && child.innerText === "") {
              continue;
            } else {
              container.appendChild(child.cloneNode(true));
              break;
            }
          }
          if (window.Quarto?.typesetMath) {
            window.Quarto.typesetMath(container);
          }
          return container.innerHTML
        } else {
          if (window.Quarto?.typesetMath) {
            window.Quarto.typesetMath(note);
          }
          return note.innerHTML;
        }
      } else {
        // Remove any anchor links if they are present
        const anchorLink = note.querySelector('a.anchorjs-link');
        if (anchorLink) {
          anchorLink.remove();
        }
        if (window.Quarto?.typesetMath) {
          window.Quarto.typesetMath(note);
        }
        if (note.classList.contains("callout")) {
          return note.outerHTML;
        } else {
          return note.innerHTML;
        }
      }
    }
    for (var i=0; i<xrefs.length; i++) {
      const xref = xrefs[i];
      tippyHover(xref, undefined, function(instance) {
        instance.disable();
        let url = xref.getAttribute('href');
        let hash = undefined; 
        if (url.startsWith('#')) {
          hash = url;
        } else {
          try { hash = new URL(url).hash; } catch {}
        }
        if (hash) {
          const id = hash.replace(/^#\/?/, "");
          const note = window.document.getElementById(id);
          if (note !== null) {
            try {
              const html = processXRef(id, note.cloneNode(true));
              instance.setContent(html);
            } finally {
              instance.enable();
              instance.show();
            }
          } else {
            // See if we can fetch this
            fetch(url.split('#')[0])
            .then(res => res.text())
            .then(html => {
              const parser = new DOMParser();
              const htmlDoc = parser.parseFromString(html, "text/html");
              const note = htmlDoc.getElementById(id);
              if (note !== null) {
                const html = processXRef(id, note);
                instance.setContent(html);
              } 
            }).finally(() => {
              instance.enable();
              instance.show();
            });
          }
        } else {
          // See if we can fetch a full url (with no hash to target)
          // This is a special case and we should probably do some content thinning / targeting
          fetch(url)
          .then(res => res.text())
          .then(html => {
            const parser = new DOMParser();
            const htmlDoc = parser.parseFromString(html, "text/html");
            const note = htmlDoc.querySelector('main.content');
            if (note !== null) {
              // This should only happen for chapter cross references
              // (since there is no id in the URL)
              // remove the first header
              if (note.children.length > 0 && note.children[0].tagName === "HEADER") {
                note.children[0].remove();
              }
              const html = processXRef(null, note);
              instance.setContent(html);
            } 
          }).finally(() => {
            instance.enable();
            instance.show();
          });
        }
      }, function(instance) {
      });
    }
        let selectedAnnoteEl;
        const selectorForAnnotation = ( cell, annotation) => {
          let cellAttr = 'data-code-cell="' + cell + '"';
          let lineAttr = 'data-code-annotation="' +  annotation + '"';
          const selector = 'span[' + cellAttr + '][' + lineAttr + ']';
          return selector;
        }
        const selectCodeLines = (annoteEl) => {
          const doc = window.document;
          const targetCell = annoteEl.getAttribute("data-target-cell");
          const targetAnnotation = annoteEl.getAttribute("data-target-annotation");
          const annoteSpan = window.document.querySelector(selectorForAnnotation(targetCell, targetAnnotation));
          const lines = annoteSpan.getAttribute("data-code-lines").split(",");
          const lineIds = lines.map((line) => {
            return targetCell + "-" + line;
          })
          let top = null;
          let height = null;
          let parent = null;
          if (lineIds.length > 0) {
              //compute the position of the single el (top and bottom and make a div)
              const el = window.document.getElementById(lineIds[0]);
              top = el.offsetTop;
              height = el.offsetHeight;
              parent = el.parentElement.parentElement;
            if (lineIds.length > 1) {
              const lastEl = window.document.getElementById(lineIds[lineIds.length - 1]);
              const bottom = lastEl.offsetTop + lastEl.offsetHeight;
              height = bottom - top;
            }
            if (top !== null && height !== null && parent !== null) {
              // cook up a div (if necessary) and position it 
              let div = window.document.getElementById("code-annotation-line-highlight");
              if (div === null) {
                div = window.document.createElement("div");
                div.setAttribute("id", "code-annotation-line-highlight");
                div.style.position = 'absolute';
                parent.appendChild(div);
              }
              div.style.top = top - 2 + "px";
              div.style.height = height + 4 + "px";
              div.style.left = 0;
              let gutterDiv = window.document.getElementById("code-annotation-line-highlight-gutter");
              if (gutterDiv === null) {
                gutterDiv = window.document.createElement("div");
                gutterDiv.setAttribute("id", "code-annotation-line-highlight-gutter");
                gutterDiv.style.position = 'absolute';
                const codeCell = window.document.getElementById(targetCell);
                const gutter = codeCell.querySelector('.code-annotation-gutter');
                gutter.appendChild(gutterDiv);
              }
              gutterDiv.style.top = top - 2 + "px";
              gutterDiv.style.height = height + 4 + "px";
            }
            selectedAnnoteEl = annoteEl;
          }
        };
        const unselectCodeLines = () => {
          const elementsIds = ["code-annotation-line-highlight", "code-annotation-line-highlight-gutter"];
          elementsIds.forEach((elId) => {
            const div = window.document.getElementById(elId);
            if (div) {
              div.remove();
            }
          });
          selectedAnnoteEl = undefined;
        };
          // Handle positioning of the toggle
      window.addEventListener(
        "resize",
        throttle(() => {
          elRect = undefined;
          if (selectedAnnoteEl) {
            selectCodeLines(selectedAnnoteEl);
          }
        }, 10)
      );
      function throttle(fn, ms) {
      let throttle = false;
      let timer;
        return (...args) => {
          if(!throttle) { // first call gets through
              fn.apply(this, args);
              throttle = true;
          } else { // all the others get throttled
              if(timer) clearTimeout(timer); // cancel #2
              timer = setTimeout(() => {
                fn.apply(this, args);
                timer = throttle = false;
              }, ms);
          }
        };
      }
        // Attach click handler to the DT
        const annoteDls = window.document.querySelectorAll('dt[data-target-cell]');
        for (const annoteDlNode of annoteDls) {
          annoteDlNode.addEventListener('click', (event) => {
            const clickedEl = event.target;
            if (clickedEl !== selectedAnnoteEl) {
              unselectCodeLines();
              const activeEl = window.document.querySelector('dt[data-target-cell].code-annotation-active');
              if (activeEl) {
                activeEl.classList.remove('code-annotation-active');
              }
              selectCodeLines(clickedEl);
              clickedEl.classList.add('code-annotation-active');
            } else {
              // Unselect the line
              unselectCodeLines();
              clickedEl.classList.remove('code-annotation-active');
            }
          });
        }
    const findCites = (el) => {
      const parentEl = el.parentElement;
      if (parentEl) {
        const cites = parentEl.dataset.cites;
        if (cites) {
          return {
            el,
            cites: cites.split(' ')
          };
        } else {
          return findCites(el.parentElement)
        }
      } else {
        return undefined;
      }
    };
    var bibliorefs = window.document.querySelectorAll('a[role="doc-biblioref"]');
    for (var i=0; i<bibliorefs.length; i++) {
      const ref = bibliorefs[i];
      const citeInfo = findCites(ref);
      if (citeInfo) {
        tippyHover(citeInfo.el, function() {
          var popup = window.document.createElement('div');
          citeInfo.cites.forEach(function(cite) {
            var citeDiv = window.document.createElement('div');
            citeDiv.classList.add('hanging-indent');
            citeDiv.classList.add('csl-entry');
            var biblioDiv = window.document.getElementById('ref-' + cite);
            if (biblioDiv) {
              citeDiv.innerHTML = biblioDiv.innerHTML;
            }
            popup.appendChild(citeDiv);
          });
          return popup.innerHTML;
        });
      }
    }
  });
  </script><div class="modal fade" id="quarto-embedded-source-code-modal" tabindex="-1" aria-labelledby="quarto-embedded-source-code-modal-label" aria-hidden="true"><div class="modal-dialog modal-dialog-scrollable"><div class="modal-content"><div class="modal-header"><h5 class="modal-title" id="quarto-embedded-source-code-modal-label">Kildekode</h5><button class="btn-close" data-bs-dismiss="modal"></button></div><div class="modal-body"><div class="">
<div class="sourceCode" id="cb5" data-shortcodes="false"><pre class="sourceCode numberSource markdown number-lines code-with-copy"><code class="sourceCode markdown"><span id="cb5-1"><a href="#cb5-1"></a><span class="co">---</span></span>
<span id="cb5-2"><a href="#cb5-2"></a><span class="an">title:</span><span class="co"> "Logo Implementation Guide"</span></span>
<span id="cb5-3"><a href="#cb5-3"></a><span class="an">subtitle:</span><span class="co"> "Quarto Best Practices for AVJ Naturfond"</span></span>
<span id="cb5-4"><a href="#cb5-4"></a><span class="an">date:</span><span class="co"> now</span></span>
<span id="cb5-5"><a href="#cb5-5"></a><span class="an">date-fomrat:</span><span class="co"> iso</span></span>
<span id="cb5-6"><a href="#cb5-6"></a><span class="co">---</span></span>
<span id="cb5-7"><a href="#cb5-7"></a></span>
<span id="cb5-8"><a href="#cb5-8"></a><span class="fu"># Logo Usage in Quarto Documents</span></span>
<span id="cb5-9"><a href="#cb5-9"></a></span>
<span id="cb5-10"><a href="#cb5-10"></a>This document demonstrates the best practices for including the AVJ Naturfond logo in Quarto documents that render to both HTML and Jupyter notebooks.</span>
<span id="cb5-11"><a href="#cb5-11"></a></span>
<span id="cb5-12"><a href="#cb5-12"></a><span class="fu">## Method 1: HTML Image Tag (Recommended)</span></span>
<span id="cb5-13"><a href="#cb5-13"></a></span>
<span id="cb5-14"><a href="#cb5-14"></a>The most reliable cross-format approach is using a standard HTML img tag with the <span class="in">`.avj-logo`</span> class:</span>
<span id="cb5-15"><a href="#cb5-15"></a></span>
<span id="cb5-16"><a href="#cb5-16"></a><span class="in">```html</span></span>
<span id="cb5-17"><a href="#cb5-17"></a><span class="op">&lt;</span>div class<span class="op">=</span><span class="st">"avj-logo"</span><span class="op">&gt;</span></span>
<span id="cb5-18"><a href="#cb5-18"></a>  <span class="op">&lt;</span>img src<span class="op">=</span><span class="st">"AVJ_NATURFOND_RGB.png"</span> alt<span class="op">=</span><span class="st">"Aage V. Jensen Naturfond Logo"</span> <span class="op">/&gt;</span></span>
<span id="cb5-19"><a href="#cb5-19"></a><span class="op">&lt;/</span>div<span class="op">&gt;</span></span>
<span id="cb5-20"><a href="#cb5-20"></a><span class="in">```</span></span>
<span id="cb5-21"><a href="#cb5-21"></a></span>
<span id="cb5-22"><a href="#cb5-22"></a><span class="dt">&lt;</span><span class="kw">div</span><span class="ot"> class</span><span class="op">=</span><span class="st">"avj-logo"</span><span class="dt">&gt;</span></span>
<span id="cb5-23"><a href="#cb5-23"></a>  <span class="dt">&lt;</span><span class="kw">img</span><span class="ot"> src</span><span class="op">=</span><span class="st">"AVJ_NATURFOND_RGB.png"</span><span class="ot"> alt</span><span class="op">=</span><span class="st">"Aage V. Jensen Naturfond Logo"</span><span class="ot"> </span><span class="dt">/&gt;</span></span>
<span id="cb5-24"><a href="#cb5-24"></a><span class="dt">&lt;/</span><span class="kw">div</span><span class="dt">&gt;</span></span>
<span id="cb5-25"><a href="#cb5-25"></a></span>
<span id="cb5-26"><a href="#cb5-26"></a><span class="fu">## Method 2: Markdown Image (Alternative)</span></span>
<span id="cb5-27"><a href="#cb5-27"></a></span>
<span id="cb5-28"><a href="#cb5-28"></a>For simple cases, you can also use Markdown syntax with custom classes:</span>
<span id="cb5-29"><a href="#cb5-29"></a></span>
<span id="cb5-30"><a href="#cb5-30"></a><span class="in">```markdown</span></span>
<span id="cb5-31"><a href="#cb5-31"></a><span class="al">![Aage V. Jensen Naturfond Logo](AVJ_NATURFOND_RGB.png)</span>{.logo}</span>
<span id="cb5-32"><a href="#cb5-32"></a><span class="in">```</span></span>
<span id="cb5-33"><a href="#cb5-33"></a></span>
<span id="cb5-34"><a href="#cb5-34"></a><span class="al">![Aage V. Jensen Naturfond Logo](AVJ_NATURFOND_RGB.png)</span>{.logo}</span>
<span id="cb5-35"><a href="#cb5-35"></a></span>
<span id="cb5-36"><a href="#cb5-36"></a><span class="fu">## Method 3: Background Image Approach</span></span>
<span id="cb5-37"><a href="#cb5-37"></a></span>
<span id="cb5-38"><a href="#cb5-38"></a>For layouts where you need text to wrap around the logo area:</span>
<span id="cb5-39"><a href="#cb5-39"></a></span>
<span id="cb5-40"><a href="#cb5-40"></a><span class="in">```html</span></span>
<span id="cb5-41"><a href="#cb5-41"></a><span class="op">&lt;</span>div class<span class="op">=</span><span class="st">"avj-logo-bg"</span><span class="op">&gt;</span></span>
<span id="cb5-42"><a href="#cb5-42"></a>  <span class="op">&lt;</span>h1<span class="op">&gt;</span>Your Content Here<span class="op">&lt;/</span>h1<span class="op">&gt;</span></span>
<span id="cb5-43"><a href="#cb5-43"></a>  <span class="op">&lt;</span>p<span class="op">&gt;</span>This content will have space reserved <span class="cf">for</span> the background logo<span class="op">.&lt;/</span>p<span class="op">&gt;</span></span>
<span id="cb5-44"><a href="#cb5-44"></a><span class="op">&lt;/</span>div<span class="op">&gt;</span></span>
<span id="cb5-45"><a href="#cb5-45"></a><span class="in">```</span></span>
<span id="cb5-46"><a href="#cb5-46"></a></span>
<span id="cb5-47"><a href="#cb5-47"></a><span class="dt">&lt;</span><span class="kw">div</span><span class="ot"> class</span><span class="op">=</span><span class="st">"avj-logo-bg"</span><span class="dt">&gt;</span></span>
<span id="cb5-48"><a href="#cb5-48"></a>  <span class="dt">&lt;</span><span class="kw">h1</span><span class="dt">&gt;</span>Example Heading<span class="dt">&lt;/</span><span class="kw">h1</span><span class="dt">&gt;</span></span>
<span id="cb5-49"><a href="#cb5-49"></a>  <span class="dt">&lt;</span><span class="kw">p</span><span class="dt">&gt;</span>This content demonstrates how text flows with the background logo approach.<span class="dt">&lt;/</span><span class="kw">p</span><span class="dt">&gt;</span></span>
<span id="cb5-50"><a href="#cb5-50"></a><span class="dt">&lt;/</span><span class="kw">div</span><span class="dt">&gt;</span></span>
<span id="cb5-51"><a href="#cb5-51"></a></span>
<span id="cb5-52"><a href="#cb5-52"></a><span class="fu">## YAML Header Method (Document-wide)</span></span>
<span id="cb5-53"><a href="#cb5-53"></a></span>
<span id="cb5-54"><a href="#cb5-54"></a>For consistent logo placement across the entire document, add this to your YAML header:</span>
<span id="cb5-55"><a href="#cb5-55"></a></span>
<span id="cb5-56"><a href="#cb5-56"></a><span class="in">```yaml</span></span>
<span id="cb5-57"><a href="#cb5-57"></a><span class="fu">format</span><span class="kw">:</span></span>
<span id="cb5-58"><a href="#cb5-58"></a><span class="at">  </span><span class="fu">html</span><span class="kw">:</span></span>
<span id="cb5-59"><a href="#cb5-59"></a><span class="at">    </span><span class="fu">theme</span><span class="kw">:</span></span>
<span id="cb5-60"><a href="#cb5-60"></a><span class="at">      </span><span class="kw">-</span><span class="at"> </span>default</span>
<span id="cb5-61"><a href="#cb5-61"></a><span class="at">      </span><span class="kw">-</span><span class="at"> </span>styles.scss</span>
<span id="cb5-62"><a href="#cb5-62"></a><span class="fu">    include-in-header</span><span class="kw">: </span><span class="ch">|</span></span>
<span id="cb5-63"><a href="#cb5-63"></a>      &lt;div class="avj-logo"&gt;</span>
<span id="cb5-64"><a href="#cb5-64"></a>        &lt;img src="AVJ_NATURFOND_RGB.png" alt="Aage V. Jensen Naturfond Logo" /&gt;</span>
<span id="cb5-65"><a href="#cb5-65"></a>      &lt;/div&gt;</span>
<span id="cb5-66"><a href="#cb5-66"></a><span class="in">```</span></span>
<span id="cb5-67"><a href="#cb5-67"></a></span>
<span id="cb5-68"><a href="#cb5-68"></a><span class="fu">## Key Improvements</span></span>
<span id="cb5-69"><a href="#cb5-69"></a></span>
<span id="cb5-70"><a href="#cb5-70"></a><span class="ss">1. </span>**Cross-format compatibility**: Works in HTML, PDF, and Jupyter notebooks</span>
<span id="cb5-71"><a href="#cb5-71"></a><span class="ss">2. </span>**Accessibility**: Proper alt text for screen readers</span>
<span id="cb5-72"><a href="#cb5-72"></a><span class="ss">3. </span>**Responsive design**: Scales appropriately on mobile devices</span>
<span id="cb5-73"><a href="#cb5-73"></a><span class="ss">4. </span>**Print-friendly**: Optimized for print output</span>
<span id="cb5-74"><a href="#cb5-74"></a><span class="ss">5. </span>**Path flexibility**: Uses relative paths that work in different contexts</span>
<span id="cb5-75"><a href="#cb5-75"></a><span class="ss">6. </span>**Semantic markup**: Uses proper HTML structure instead of CSS content injection</span>
<span id="cb5-76"><a href="#cb5-76"></a></span>
<span id="cb5-77"><a href="#cb5-77"></a><span class="fu">## Usage Recommendations</span></span>
<span id="cb5-78"><a href="#cb5-78"></a></span>
<span id="cb5-79"><a href="#cb5-79"></a><span class="ss">- </span>Use **Method 1** (HTML img tag) for most cases</span>
<span id="cb5-80"><a href="#cb5-80"></a><span class="ss">- </span>Use **Method 2** (Markdown) for simple, inline logos</span>
<span id="cb5-81"><a href="#cb5-81"></a><span class="ss">- </span>Use **Method 3** (background) when you need text to flow around the logo area</span>
<span id="cb5-82"><a href="#cb5-82"></a><span class="ss">- </span>Use **YAML header method** for document-wide logo placement</span>
<span id="cb5-83"><a href="#cb5-83"></a></span>
<span id="cb5-84"><a href="#cb5-84"></a>The refactored CSS now provides robust, cross-platform logo rendering that follows Quarto best practices.</span></code><button title="Kopier til udklipsholder" class="code-copy-button" data-in-quarto-modal=""><i class="bi"></i></button></pre></div>
</div></div></div></div></div>
</div> <!-- /content -->




<script src="logo_usage_guide_files/libs/quarto-html/zenscroll-min.js"></script>
</body></html>