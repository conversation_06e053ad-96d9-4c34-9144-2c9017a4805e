{"folders": [{"path": "."}], "settings": {"jupyter.askForKernelRestart": false, "notebook.markup.fontSize": 14, "notebook.output.textLineLimit": 30, "quarto.render.renderOnSave": false, "notebook.lineNumbers": "on", "files.associations": {"*.qmd": "quarto", "*.ipynb": "jupyter-notebook", "*.csv": "csv (semicolon)"}, "terminal.integrated.defaultProfile.windows": "Command Prompt", "editor.rulers": [80, 120], "python.defaultInterpreterPath": "C:\\AnacondaPath\\envs\\jupyter_env\\python.exe", "jupyter.alwaysTrustNotebooks": true, "jupyter.interactiveWindow.textEditor.executeSelection": true, "jupyter.exportWithOutputs": true, "[python]": {"editor.rulers": [80], "editor.wordWrap": "bounded", "editor.wordWrapColumn": 80, "editor.defaultFormatter": "ms-python.black-formatter"}, "[markdown]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.wordWrap": "bounded", "editor.wordWrapColumn": 120, "editor.rulers": [80, 120]}, "[quarto]": {"editor.defaultFormatter": "quarto.quarto", "editor.wordWrap": "bounded", "editor.wordWrapColumn": 120, "editor.rulers": [80, 120]}}}